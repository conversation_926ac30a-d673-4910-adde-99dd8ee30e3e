// 测试声音分组并发优化逻辑
const { performance } = require('perf_hooks');

// 模拟对话数据
const testDialogue = [
  { voice: "<PERSON>", text: "大家好！我是Adam。" },      // 0
  { voice: "Alice", text: "你好Adam，我是Alice。" },   // 1  
  { voice: "<PERSON>", text: "很高兴认识大家。" },          // 2
  { voice: "<PERSON>", text: "Alice，你来自哪里？" },      // 3
  { voice: "Alice", text: "我来自北京。" },           // 4
  { voice: "<PERSON>", text: "我也是北京的！" },           // 5
  { voice: "Adam", text: "真巧！" },                 // 6
  { voice: "Alice", text: "是的，很有缘分。" },        // 7
  { voice: "<PERSON>", text: "我们可以一起聊聊北京。" },     // 8
  { voice: "Adam", text: "好主意！" }                // 9
];

// 测试声音分组逻辑
function testVoiceGrouping() {
  console.log('🧪 测试声音分组逻辑...\n');
  
  const startTime = performance.now();
  
  // 第一步：按声音分组，保持原始位置信息
  const voiceGroups = new Map();
  testDialogue.forEach((speaker, index) => {
    if (!voiceGroups.has(speaker.voice)) {
      voiceGroups.set(speaker.voice, []);
    }
    voiceGroups.get(speaker.voice).push({
      ...speaker,
      originalIndex: index
    });
  });

  const uniqueVoices = Array.from(voiceGroups.keys());
  console.log(`📊 分组结果：`);
  console.log(`总说话者：${testDialogue.length} 人次`);
  console.log(`不同声音：${uniqueVoices.length} 种`);
  console.log('');

  voiceGroups.forEach((speakers, voice) => {
    const positions = speakers.map(s => s.originalIndex);
    console.log(`🎤 ${voice}: ${speakers.length} 句话，位置 [${positions.join(', ')}]`);
    speakers.forEach(speaker => {
      console.log(`   [${speaker.originalIndex}] "${speaker.text}"`);
    });
    console.log('');
  });

  // 模拟重组过程
  console.log('🔧 测试音频重组逻辑...');
  const finalAudioArray = new Array(testDialogue.length);
  
  // 模拟每个声音组的结果
  voiceGroups.forEach((speakers, voice) => {
    speakers.forEach(speaker => {
      finalAudioArray[speaker.originalIndex] = `${voice}_audio_${speaker.originalIndex}`;
    });
  });

  console.log('📋 重组后的音频顺序：');
  finalAudioArray.forEach((audio, index) => {
    console.log(`  [${index}] ${audio} -> "${testDialogue[index].text}"`);
  });

  // 验证顺序正确性
  let orderCorrect = true;
  for (let i = 0; i < testDialogue.length; i++) {
    const expectedVoice = testDialogue[i].voice;
    const actualVoice = finalAudioArray[i].split('_')[0];
    if (expectedVoice !== actualVoice) {
      orderCorrect = false;
      console.log(`❌ 位置 ${i} 顺序错误: 期望 ${expectedVoice}, 实际 ${actualVoice}`);
    }
  }

  const endTime = performance.now();
  console.log('');
  console.log(`✅ 顺序验证: ${orderCorrect ? '正确' : '错误'}`);
  console.log(`⏱️  处理时间: ${(endTime - startTime).toFixed(2)}ms`);
  
  return { voiceGroups, orderCorrect };
}

// 性能对比分析
function performanceAnalysis() {
  console.log('\n📈 性能对比分析...\n');
  
  const voiceCount = new Map();
  testDialogue.forEach(speaker => {
    voiceCount.set(speaker.voice, (voiceCount.get(speaker.voice) || 0) + 1);
  });

  console.log('🐌 原始串行方式：');
  console.log(`  数据库查询次数: ${testDialogue.length} 次`);
  console.log(`  处理时间单位: ${testDialogue.length} 个时间单位（完全串行）`);
  console.log('  详细查询：');
  testDialogue.forEach((speaker, index) => {
    console.log(`    [${index}] 查询 ${speaker.voice} 的语音ID`);
  });

  console.log('\n🚀 优化并发方式：');
  const uniqueVoices = Array.from(voiceCount.keys());
  console.log(`  数据库查询次数: ${uniqueVoices.length} 次`);
  console.log(`  处理时间单位: ${Math.max(...voiceCount.values())} 个时间单位（最长组的时间）`);
  console.log('  详细查询：');
  uniqueVoices.forEach(voice => {
    console.log(`    查询 ${voice} 的语音ID (用于 ${voiceCount.get(voice)} 句话)`);
  });

  console.log('\n📊 性能提升：');
  const dbQueryReduction = ((testDialogue.length - uniqueVoices.length) / testDialogue.length * 100).toFixed(1);
  const timeReduction = ((testDialogue.length - Math.max(...voiceCount.values())) / testDialogue.length * 100).toFixed(1);
  
  console.log(`  数据库查询减少: ${dbQueryReduction}% (${testDialogue.length} → ${uniqueVoices.length})`);
  console.log(`  处理时间减少: ${timeReduction}% (${testDialogue.length} → ${Math.max(...voiceCount.values())})`);
}

// 运行测试
if (require.main === module) {
  console.log('🎯 声音分组并发优化测试\n');
  console.log('=' .repeat(50));
  
  const result = testVoiceGrouping();
  performanceAnalysis();
  
  console.log('\n' + '='.repeat(50));
  console.log(`🎉 测试完成: ${result.orderCorrect ? '成功' : '失败'}`);
}

module.exports = { testVoiceGrouping, performanceAnalysis };

# ========== 基础配置 ==========
NODE_ENV=development
PORT=3001
DEBUG=true

# ========== API配置 ==========
# API基础URL，用于生成完整的下载链接
API_BASE_URL=http://localhost:3001

# ========== 数据库配置 ==========
DATABASE_URL="postgresql://tts_app_user:TTS_DB_2024_SecurePass!@localhost:5432/tts_app_db"
REDIS_URL="redis://localhost:6379"

# ========== JWT认证配置 ==========
JWT_SECRET="X8k9#mP2$vL5nQ7@jR3wY6*tZ4"
ACCESS_TOKEN_EXPIRE=7200
REFRESH_TOKEN_EXPIRE=604800
SALT_ROUNDS=10

# ========== 管理员配置 ==========
ADMIN_USERS="1060352824"


# ========== 腾讯云SES邮件配置 ==========
TENCENT_SECRET_ID="AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9"
TENCENT_SECRET_KEY="NWUHtIWahkeHl6IMK6f9Zqvl3EF5g8PT"
SES_REGION="ap-guangzhou"
FROM_EMAIL="<EMAIL>"
FROM_EMAIL_NAME="tts.aispeak.top"
VERIFICATION_TEMPLATE_ID="32699"

# ========== 文件存储配置 ==========
AUDIO_STORAGE_PATH="/var/data/tts-app/audios"

# ========== R2存储配置 ==========
R2_PATH_PREFIX="audios"
R2_DIRECT_DOMAIN="r2-proxy-assets.aispeak.top"

# ========== 代理认证配置 ==========
PROXY_SECRET="AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9"

# ========== TTS代理配置 ==========
# 是否启用TTS代理功能 (true/false)
# true: 启用代理功能，支持多种工作模式
# false: 禁用代理，仅使用直连ElevenLabs API
ENABLE_TTS_PROXY=true

# 代理服务器URL列表，多个URL用逗号分隔
# 支持多个代理服务器实现负载均衡和故障转移
# 格式: "https://proxy1.example.com,https://proxy2.example.com"
TTS_PROXY_URLS="https://tts-proxy-hk-1.aispeak.top,https://tts-proxy-hk-2.aispeak.top,https://tts-proxy-hk-3.aispeak.top"

# 代理服务器认证密钥
# 用于验证客户端身份，通过x-proxy-secret头部发送
TTS_PROXY_SECRET="AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9"

# 代理工作模式，决定何时使用代理
# "direct": 仅直连模式，不使用代理
# "proxy": 仅代理模式，强制使用代理
# "fallback": 故障转移模式，直连失败后自动切换到代理 (推荐)
# "balanced": 负载均衡模式，按比例分配直连和代理请求
TTS_PROXY_MODE="fallback"

# 代理请求超时时间 (毫秒)
# 单个代理请求的最大等待时间，超时后会尝试其他代理或直连
TTS_PROXY_TIMEOUT=45000

# 单个代理的重试次数
# 当代理请求失败时，会重试指定次数后再尝试下一个代理
TTS_PROXY_RETRY_COUNT=2

# 负载均衡比例 (0.0-1.0)
# 当模式为"balanced"时，此比例的请求会走代理，其余走直连
# 例如: 0.3 表示30%的请求使用代理，70%使用直连
TTS_PROXY_BALANCE_RATIO=0.3

# 故障转移阈值
# 连续失败N次后，会优先使用代理而不是直连
TTS_FALLBACK_THRESHOLD=2

# 故障检测时间窗口 (秒)
# 在此时间窗口内统计失败次数，用于故障转移决策
TTS_FALLBACK_WINDOW=300

# 代理选择策略
# "sequential": 按配置顺序依次尝试代理 (故障转移)
# "random": 每次随机选择代理 (负载均衡 + 故障转移)
TTS_PROXY_SELECTION_STRATEGY="random"

# ========== 日志配置 ==========
LOG_DIR="/var/log/tts-app"

# 日志文件轮转配置
# 单个日志文件最大大小 (支持 B, KB, MB, GB)
# 默认: 50MB，建议: 开发环境 10-50MB，生产环境 50-100MB
LOG_MAX_FILE_SIZE=50MB

# 保留的日志文件数量
# 默认: 30，建议: 开发环境 7-14，生产环境 30-90
LOG_MAX_FILES=30

# 是否启用日志压缩 (true/false)
# 默认: true，建议: 生产环境启用以节省磁盘空间
LOG_ENABLE_COMPRESSION=true

# 日志清理检查间隔 (毫秒)
# 默认: 86400000 (24小时)，建议: 12-24小时
LOG_CLEANUP_INTERVAL=86400000

# ========== 集群级重试配置 ==========
# 集群级重试次数
# 当所有代理都失败时，整个集群会重试的次数
TTS_CLUSTER_RETRY_COUNT=3

# 集群重试最大延迟时间 (毫秒)
# 集群级重试时的最大等待时间
TTS_CLUSTER_MAX_DELAY=8000

# 单个代理重试最大延迟时间 (毫秒)
# 单个代理失败重试时的最大等待时间
TTS_SINGLE_MAX_DELAY=5000

# 直连重试最大延迟时间 (毫秒)
# 直连ElevenLabs API失败重试时的最大等待时间
TTS_DIRECT_MAX_DELAY=8000

# 是否启用指数退避算法 (true/false)
# true: 重试间隔会逐渐增加，避免频繁请求
# false: 使用固定间隔重试
TTS_ENABLE_BACKOFF=true

# ========== 任务级重试配置 ==========
# 是否启用任务级重试功能 (true/false)
# true: 整个TTS任务失败时会自动重试，使用不同的策略
# false: 任务失败后不重试，直接返回错误
ENABLE_TASK_RETRY=true

# 任务最大重试次数
# 整个TTS任务失败后的最大重试次数 (总共尝试次数 = 1 + 重试次数)
MAX_TASK_RETRIES=2

# 第一次重试延迟时间 (毫秒)
# 任务首次失败后，等待此时间再进行第一次重试
TASK_RETRY_DELAY_1=6000

# 第二次重试延迟时间 (毫秒)
# 第一次重试失败后，等待此时间再进行第二次重试
TASK_RETRY_DELAY_2=12000

# 任务绝对超时时间 (毫秒)
# 单个TTS任务的最大执行时间，超过此时间会强制终止
# 防止任务无限期等待，建议设置为10分钟 (600000ms)
TASK_ABSOLUTE_TIMEOUT=600000

# 是否启用任务重试调试日志 (true/false)
# true: 输出详细的重试过程日志，便于调试
# false: 只输出基本日志，减少日志量
ENABLE_TASK_RETRY_DEBUG=false

# ========== 智能超时配置 ==========
TTS_INIT_TIMEOUT=30000
TTS_TEXT_PROCESSING_TIMEOUT=60000
TTS_AUDIO_MERGING_TIMEOUT=120000
TTS_FILE_STORAGE_TIMEOUT=60000
TTS_DEFAULT_TIMEOUT=300000
TTS_CHUNK_TIMEOUT=40000
TTS_MIN_TIMEOUT=120000
TTS_MAX_TIMEOUT=900000
TTS_ENABLE_COMPLEXITY_ADJUSTMENT=true
TTS_LARGE_CHUNK_THRESHOLD=10
TTS_HUGE_CHUNK_THRESHOLD=20
TTS_LARGE_TEXT_THRESHOLD=5000
TTS_HUGE_TEXT_THRESHOLD=10000
TTS_ENABLE_TIMEOUT_DEBUG=false

# ========== 进度消息配置 ==========
ENABLE_PROGRESS_MESSAGES=false
ENABLE_DEBUG_PROGRESS=false

# ========== 代理统计和调试配置 ==========
# 是否启用代理统计功能 (true/false)
# true: 收集代理使用统计信息，如成功率、响应时间等
# false: 不收集统计信息，减少系统开销
ENABLE_PROXY_STATS=true

# 是否启用代理调试日志 (true/false)
# true: 输出详细的代理调用日志，包括URL选择、请求响应等
# false: 只输出基本日志，适合生产环境
ENABLE_PROXY_DEBUG=false

# ========== TTS路径追踪日志配置 ==========
# 是否启用TTS路径追踪日志 (true/false)
# true: 输出详细的TTS生成路径日志，包括路由决策、网络模式、代理信息等
# false: 只输出基本TTS日志，适合生产环境
ENABLE_TTS_ROUTE_LOGGING=true

# TTS路径日志级别 (basic/detailed/verbose)
# basic: 只记录关键路径信息（路由决策、成功/失败）
# detailed: 记录详细路径信息（包含代理、节点、耗时等）
# verbose: 记录所有调试信息（包含请求参数、响应详情等）
TTS_ROUTE_LOG_LEVEL=detailed

# 是否启用网关节点追踪日志 (true/false)
# true: 输出sing-box节点切换、健康检查等详细日志
# false: 只输出基本网关日志
ENABLE_GATEWAY_NODE_LOGGING=true

# 是否启用网络请求详细日志 (true/false)
# true: 输出每个网络请求的详细信息（URL、耗时、状态码等）
# false: 只输出基本网络日志
ENABLE_NETWORK_REQUEST_LOGGING=true

# ========== 自动标注服务配置 ==========
# 自动生成标签API配置（安全存储，不暴露给前端）
AUTO_TAG_API_URL=https://geminitts.aispeak.top/api/tts/process
AUTO_TAG_TOKEN=CM8l3Wqf7TaWah7ruIAxAmMZYcAd274MAeAnFkhvxPg0TMPs
AUTO_TAG_TIMEOUT=30000
AUTO_TAG_RATE_LIMIT=10

# ========== 网络模式配置 ==========
# 网络模式: direct, proxy, gateway, fallback
NETWORK_MODE=gateway

# ========== sing-box代理网关配置 ==========
# 启用sing-box代理网关
ENABLE_SINGBOX_GATEWAY=true

# sing-box控制API配置
SINGBOX_API_ENDPOINT=http://127.0.0.1:9090
SINGBOX_SELECTOR_NAME=proxy-selector

# 本地代理端口配置
SINGBOX_PROXY_HOST=127.0.0.1
SINGBOX_PROXY_PORT=1080

# 节点健康管理配置
SINGBOX_HEALTH_CHECK_INTERVAL=30000
SINGBOX_NODE_TIMEOUT=15000
SINGBOX_MAX_RETRIES=3
SINGBOX_RETRY_DELAY=1000

# 节点订阅配置
SINGBOX_SUBSCRIBE_URL=
SINGBOX_AUTO_UPDATE=false
SINGBOX_UPDATE_INTERVAL=86400000

# 调试和监控配置
SINGBOX_DEBUG=false
SINGBOX_ENABLE_STATS=true

# 降级和容错配置
SINGBOX_FALLBACK_ENABLED=true
SINGBOX_FALLBACK_THRESHOLD=3
SINGBOX_FALLBACK_WINDOW=300000

# ========== 工作池配置 ==========
# 工作池大小 - 并发代理工人数量
# 默认: 10，建议: 根据服务器性能和并发需求调整，通常5-20
SINGBOX_WORKER_POOL_SIZE=10

# 工人端口起始 - 工人专用代理端口的起始值
# 默认: 1081，说明: 工人将使用 1081, 1082, 1083... 等端口
SINGBOX_WORKER_PORT_START=1081

# 工人选择器前缀 - sing-box配置中工人选择器的命名前缀
# 默认: worker-selector，说明: 将生成 worker-selector-1, worker-selector-2... 等选择器
SINGBOX_WORKER_SELECTOR_PREFIX=worker-selector

# 工人入口前缀 - sing-box配置中工人入口的命名前缀
# 默认: worker-in，说明: 将生成 worker-in-1, worker-in-2... 等入口
SINGBOX_WORKER_INBOUND_PREFIX=worker-in

# ========== 混合健康检查策略配置 ==========
# 隔离池检查间隔（毫秒）- 后台修复任务的执行频率
# 默认: 600000 (10分钟)，建议: 开发环境 300000 (5分钟)，生产环境 600000-1800000 (10-30分钟)
# 说明: 控制后台修复任务检查隔离池中节点的频率，值越小检查越频繁但资源消耗越大
SINGBOX_QUARANTINE_CHECK_INTERVAL=600000

# 隔离池健康检查超时（毫秒）- 单次健康检查的最大等待时间
# 默认: 5000 (5秒)，建议: 3000-8000，根据网络环境调整
# 说明: 对隔离池中节点进行健康检查时的超时时间，避免长时间等待
SINGBOX_QUARANTINE_HEALTH_CHECK_TIMEOUT=5000

# 临时隔离恢复所需连续成功次数
# 默认: 2，建议: 2-3次
# 说明: 临时隔离节点（网络错误、超时等）需要连续N次健康检查成功才能恢复到健康池
SINGBOX_QUARANTINE_RECOVERY_THRESHOLD=2

# 永久隔离恢复所需连续成功次数
# 默认: 3，建议: 3-5次，应该比临时隔离更严格
# 说明: 永久隔离节点（配额超限、403/429错误等）需要连续N次健康检查成功才能恢复
SINGBOX_QUARANTINE_PERMANENT_RECOVERY_THRESHOLD=3

# 是否允许永久隔离节点恢复
# 默认: true，建议: 开发/测试环境 true，生产环境根据运维策略决定
# true: 允许永久隔离节点通过后台检查自动恢复
# false: 永久隔离节点不参与后台检查，需要手动恢复
SINGBOX_QUARANTINE_ENABLE_PERMANENT_RECOVERY=false
/**
 * 声音分组并发优化验证测试脚本
 * 
 * 此脚本通过WebSocket连接测试多人对话TTS处理，
 * 验证声音分组并发优化是否正确实施
 */

const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');

class VoiceGroupingVerificationTest {
  constructor() {
    this.ws = null;
    this.testResults = [];
    this.startTime = null;
    this.endTime = null;
    this.progressLogs = [];
    this.ttsRequestTimes = [];
  }

  // 测试用的多人对话数据 - 设计用于验证声音分组
  getTestDialogue() {
    return {
      taskType: 'dialogue',
      dialogue: [
        { voice: 'Alice', text: '大家好，我是Alice，今天天气真不错！' },           // 声音组1-第1句
        { voice: 'Bob', text: '你好Alice，我是Bob。' },                      // 声音组2-第1句  
        { voice: 'Charlie', text: '嗨，我是Charlie，很高兴见到大家。' },        // 声音组3-第1句
        { voice: 'Alice', text: 'Bob和Charlie，你们今天都在做什么呢？' },       // 声音组1-第2句
        { voice: 'Bob', text: '我在学习新的编程技术。' },                     // 声音组2-第2句
        { voice: 'Charlie', text: '我在写一些测试代码，很有趣的工作。' },        // 声音组3-第2句
        { voice: 'Alice', text: '听起来都很棒！我们都在不断学习和进步。' },      // 声音组1-第3句
        { voice: 'Bob', text: '是的，持续学习很重要。' },                     // 声音组2-第3句
      ],
      model: 'eleven_v3',
      stability: 0.5,
      similarity_boost: 0.8,
      style: 0.0,
      speed: 1.0
    };
  }

  // 连接WebSocket
  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      console.log('🔗 连接到多人对话WebSocket...');
      this.ws = new WebSocket('ws://localhost:3001/api/tts/ws/dialogue/generate');
      
      this.ws.on('open', () => {
        console.log('✅ WebSocket连接已建立');
        resolve();
      });

      this.ws.on('error', (error) => {
        console.error('❌ WebSocket连接错误:', error);
        reject(error);
      });

      this.ws.on('message', (data) => {
        this.handleMessage(data);
      });

      this.ws.on('close', () => {
        console.log('🔌 WebSocket连接已关闭');
      });
    });
  }

  // 处理WebSocket消息
  handleMessage(data) {
    try {
      const message = JSON.parse(data.toString());
      const timestamp = new Date().toISOString();
      
      console.log(`📨 [${timestamp}] 收到消息:`, message);
      
      // 记录进度信息
      if (message.type === 'progress') {
        this.progressLogs.push({
          timestamp,
          message: message.message,
          percentage: message.percentage
        });
      }
      
      // 记录完成信息
      if (message.type === 'complete') {
        this.endTime = Date.now();
        console.log('🎉 任务完成！');
        this.analyzeResults();
      }
      
      // 记录错误信息
      if (message.type === 'error') {
        console.error('❌ 任务错误:', message.message);
        this.endTime = Date.now();
        this.analyzeResults();
      }
    } catch (error) {
      console.error('解析消息失败:', error);
    }
  }

  // 发送测试请求
  async sendTestRequest() {
    const testData = this.getTestDialogue();
    
    console.log('📤 发送测试数据...');
    console.log('🎭 测试对话包含:');
    console.log(`   - 总句数: ${testData.dialogue.length}`);
    console.log(`   - 声音种类: ${new Set(testData.dialogue.map(d => d.voice)).size}`);
    console.log(`   - Alice: ${testData.dialogue.filter(d => d.voice === 'Alice').length} 句`);
    console.log(`   - Bob: ${testData.dialogue.filter(d => d.voice === 'Bob').length} 句`);
    console.log(`   - Charlie: ${testData.dialogue.filter(d => d.voice === 'Charlie').length} 句`);
    
    this.startTime = Date.now();
    this.ws.send(JSON.stringify(testData));
  }

  // 分析测试结果
  analyzeResults() {
    const totalTime = this.endTime - this.startTime;
    
    console.log('\n📊 ===== 测试结果分析 =====');
    console.log(`⏱️  总处理时间: ${totalTime}ms (${(totalTime/1000).toFixed(2)}秒)`);
    console.log(`📝 进度日志数量: ${this.progressLogs.length}`);
    
    // 分析进度日志
    console.log('\n📋 进度日志分析:');
    this.progressLogs.forEach((log, index) => {
      console.log(`   ${index + 1}. [${log.timestamp}] ${log.message} ${log.percentage ? `(${log.percentage}%)` : ''}`);
    });
    
    // 检查是否有声音分组相关的进度信息
    const voiceGroupingLogs = this.progressLogs.filter(log => 
      log.message.includes('声音') || 
      log.message.includes('并发') || 
      log.message.includes('分组') ||
      log.message.includes('Alice') ||
      log.message.includes('Bob') ||
      log.message.includes('Charlie')
    );
    
    console.log('\n🎯 声音分组相关日志:');
    if (voiceGroupingLogs.length > 0) {
      voiceGroupingLogs.forEach(log => {
        console.log(`   ✅ ${log.message}`);
      });
    } else {
      console.log('   ⚠️  未发现声音分组相关的进度日志');
    }
    
    // 生成测试报告
    this.generateReport();
    
    // 关闭连接
    setTimeout(() => {
      this.ws.close();
      process.exit(0);
    }, 1000);
  }

  // 生成详细测试报告
  generateReport() {
    const report = {
      testInfo: {
        timestamp: new Date().toISOString(),
        totalProcessingTime: this.endTime - this.startTime,
        dialogueLength: 8,
        uniqueVoices: 3,
        voiceDistribution: {
          Alice: 3,
          Bob: 3,
          Charlie: 2
        }
      },
      progressLogs: this.progressLogs,
      analysis: {
        hasVoiceGroupingLogs: this.progressLogs.some(log => 
          log.message.includes('声音') || log.message.includes('并发')
        ),
        totalProgressSteps: this.progressLogs.length,
        estimatedConcurrencyBenefit: '基于3个不同声音，理论上可以实现3倍并发加速'
      }
    };
    
    const reportPath = path.join(__dirname, '本地开发日志', 'voice_grouping_test_report.json');
    
    // 确保目录存在
    const dir = path.dirname(reportPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
    console.log(`\n📄 详细测试报告已保存到: ${reportPath}`);
  }

  // 运行测试
  async runTest() {
    try {
      console.log('🚀 开始声音分组并发优化验证测试...\n');
      
      await this.connectWebSocket();
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待连接稳定
      await this.sendTestRequest();
      
      // 等待测试完成（最多5分钟）
      setTimeout(() => {
        if (!this.endTime) {
          console.log('⏰ 测试超时，强制结束');
          this.endTime = Date.now();
          this.analyzeResults();
        }
      }, 5 * 60 * 1000);
      
    } catch (error) {
      console.error('❌ 测试执行失败:', error);
      process.exit(1);
    }
  }
}

// 运行测试
if (require.main === module) {
  const test = new VoiceGroupingVerificationTest();
  test.runTest().catch(console.error);
}

module.exports = VoiceGroupingVerificationTest;

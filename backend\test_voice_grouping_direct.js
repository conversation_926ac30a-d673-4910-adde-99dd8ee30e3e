/**
 * 直接测试声音分组逻辑的脚本
 * 
 * 此脚本直接调用TTS处理器，绕过WebSocket，
 * 专门验证声音分组并发优化的内部逻辑
 */

const TTSProcessor = require('./src/services/ttsProcessor');
const { v4: uuidv4 } = require('uuid');

class DirectVoiceGroupingTest {
  constructor() {
    this.processor = new TTSProcessor();
    this.testResults = [];
    this.originalConsoleLog = console.log;
    this.capturedLogs = [];
  }

  // 捕获console.log输出
  captureConsoleLogs() {
    console.log = (...args) => {
      const logMessage = args.join(' ');
      this.capturedLogs.push({
        timestamp: new Date().toISOString(),
        message: logMessage
      });
      this.originalConsoleLog(...args);
    };
  }

  // 恢复console.log
  restoreConsoleLogs() {
    console.log = this.originalConsoleLog;
  }

  // 模拟测试数据
  getTestData() {
    return {
      taskType: 'dialogue',
      dialogue: [
        { voice: 'Alice', text: '这是Alice的第一句话，用于测试声音分组。' },
        { voice: '<PERSON>', text: '这是Bob的第一句话。' },
        { voice: 'Charlie', text: '这是Charlie的第一句话。' },
        { voice: 'Alice', text: '这是Alice的第二句话，应该与第一句在同一组。' },
        { voice: 'Bob', text: '这是Bob的第二句话，也应该与Bob的第一句在同一组。' },
        { voice: 'Alice', text: '这是Alice的第三句话。' },
      ],
      model: 'eleven_v3',
      stability: 0.5,
      similarity_boost: 0.8,
      style: 0.0,
      speed: 1.0
    };
  }

  // 模拟publishProgress方法来捕获内部进度
  mockPublishProgress() {
    const originalPublishProgress = this.processor.publishProgress;
    
    this.processor.publishProgress = async (taskId, progress, options = {}) => {
      const timestamp = new Date().toISOString();
      const progressMessage = typeof progress === 'string' ? progress : progress.message;
      
      this.testResults.push({
        timestamp,
        taskId,
        message: progressMessage,
        options,
        isInternal: options.internal || false
      });
      
      console.log(`[PROGRESS-CAPTURED] ${taskId}: ${progressMessage} ${options.internal ? '(内部)' : '(用户)'}`);
      
      // 调用原始方法（但不会真正发布到Redis）
      return originalPublishProgress.call(this.processor, taskId, progress, options);
    };
  }

  // 模拟Redis和数据库操作
  mockExternalDependencies() {
    // 模拟Redis操作
    const mockRedisClient = {
      setTaskData: async () => true,
      publishProgress: async () => true
    };
    
    // 模拟数据库操作
    const mockGetVoiceId = async (voiceName) => {
      const voiceMap = {
        'Alice': 'voice-id-alice-123',
        'Bob': 'voice-id-bob-456', 
        'Charlie': 'voice-id-charlie-789'
      };
      
      console.log(`[MOCK-DB] 查询声音ID: ${voiceName} -> ${voiceMap[voiceName]}`);
      
      // 模拟数据库查询延迟
      await new Promise(resolve => setTimeout(resolve, 50));
      
      return voiceMap[voiceName] || 'default-voice-id';
    };
    
    // 模拟文本分割
    const mockSplitText = async (text) => {
      console.log(`[MOCK-SPLIT] 分割文本: "${text.substring(0, 30)}..."`);
      return [text]; // 简化：不实际分割
    };
    
    // 模拟音频处理
    const mockProcessChunks = async (chunks, voiceId, options) => {
      console.log(`[MOCK-AUDIO] 处理音频块: ${chunks.length}个块, voiceId: ${voiceId}`);
      
      // 模拟音频生成延迟
      const processingTime = Math.random() * 1000 + 500; // 500-1500ms
      await new Promise(resolve => setTimeout(resolve, processingTime));
      
      return chunks.map(() => Buffer.from('mock-audio-data'));
    };
    
    // 模拟音频合并
    const mockCombineAudio = (audioList) => {
      console.log(`[MOCK-COMBINE] 合并音频: ${audioList.length}个音频片段`);
      return Buffer.from('combined-mock-audio-data');
    };
    
    // 模拟VIP检查
    const mockCheckVip = async () => {
      console.log(`[MOCK-VIP] VIP权限检查通过`);
      return true;
    };
    
    // 模拟用户使用量更新
    const mockUpdateUserUsage = async () => {
      console.log(`[MOCK-USAGE] 用户使用量更新完成`);
      return true;
    };
    
    // 替换全局依赖
    global.redisClient = mockRedisClient;
    global.getVoiceId = mockGetVoiceId;
    global.splitText = mockSplitText;
    global.processChunks = mockProcessChunks;
    global.combineAudio = mockCombineAudio;
    global.checkVip = mockCheckVip;
    global.updateUserUsage = mockUpdateUserUsage;
  }

  // 运行测试
  async runTest() {
    console.log('🚀 开始直接测试声音分组逻辑...\n');
    
    // 设置测试环境
    this.captureConsoleLogs();
    this.mockPublishProgress();
    this.mockExternalDependencies();
    
    const taskId = uuidv4();
    const testData = this.getTestData();
    const username = 'test-user';
    
    console.log('📋 测试数据:');
    console.log(`   任务ID: ${taskId}`);
    console.log(`   对话句数: ${testData.dialogue.length}`);
    console.log(`   声音种类: ${new Set(testData.dialogue.map(d => d.voice)).size}`);
    
    testData.dialogue.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.voice}: "${item.text.substring(0, 30)}..."`);
    });
    
    console.log('\n⏱️  开始处理...\n');
    
    const startTime = Date.now();
    
    try {
      // 调用startDialogue方法
      const result = await this.processor.startDialogue(taskId, testData, username);
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      console.log('\n✅ 处理完成！');
      console.log(`⏱️  总耗时: ${totalTime}ms`);
      
      this.analyzeResults(totalTime);
      
    } catch (error) {
      console.error('\n❌ 处理失败:', error);
      this.analyzeResults(Date.now() - startTime, error);
    } finally {
      this.restoreConsoleLogs();
    }
  }

  // 分析测试结果
  analyzeResults(totalTime, error = null) {
    console.log('\n📊 ===== 测试结果分析 =====');
    
    if (error) {
      console.log('❌ 测试状态: 失败');
      console.log('❌ 错误信息:', error.message);
    } else {
      console.log('✅ 测试状态: 成功');
    }
    
    console.log(`⏱️  总处理时间: ${totalTime}ms`);
    console.log(`📝 捕获的进度日志: ${this.testResults.length}条`);
    
    // 分析进度日志
    console.log('\n📋 进度日志详情:');
    this.testResults.forEach((result, index) => {
      const type = result.isInternal ? '[内部]' : '[用户]';
      console.log(`   ${index + 1}. ${type} ${result.message}`);
    });
    
    // 检查声音分组相关日志
    const voiceGroupingLogs = this.testResults.filter(result => 
      result.message.includes('声音') || 
      result.message.includes('并发') || 
      result.message.includes('分组') ||
      result.message.includes('Alice') ||
      result.message.includes('Bob') ||
      result.message.includes('Charlie')
    );
    
    console.log('\n🎯 声音分组优化验证:');
    if (voiceGroupingLogs.length > 0) {
      console.log('✅ 发现声音分组相关日志:');
      voiceGroupingLogs.forEach(log => {
        console.log(`   - ${log.message}`);
      });
    } else {
      console.log('⚠️  未发现声音分组相关日志');
    }
    
    // 检查数据库查询优化
    const dbQueryLogs = this.capturedLogs.filter(log => 
      log.message.includes('[MOCK-DB]')
    );
    
    console.log('\n🗄️  数据库查询分析:');
    console.log(`   总查询次数: ${dbQueryLogs.length}`);
    console.log(`   预期查询次数: 3 (Alice, Bob, Charlie各一次)`);
    
    if (dbQueryLogs.length === 3) {
      console.log('✅ 数据库查询优化生效！每个声音只查询一次');
    } else if (dbQueryLogs.length === 6) {
      console.log('❌ 数据库查询未优化，每个说话者都查询了一次');
    } else {
      console.log(`⚠️  查询次数异常: ${dbQueryLogs.length}`);
    }
    
    dbQueryLogs.forEach(log => {
      console.log(`   - ${log.message}`);
    });
    
    console.log('\n🎯 结论:');
    if (voiceGroupingLogs.length > 0 && dbQueryLogs.length === 3) {
      console.log('🎉 声音分组并发优化验证成功！');
    } else {
      console.log('⚠️  声音分组并发优化可能未完全生效');
    }
  }
}

// 运行测试
if (require.main === module) {
  const test = new DirectVoiceGroupingTest();
  test.runTest().catch(console.error);
}

module.exports = DirectVoiceGroupingTest;

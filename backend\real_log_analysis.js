/**
 * 实际日志分析脚本
 * 
 * 分析真实的后端日志，验证声音分组并发优化的实际效果
 */

const fs = require('fs');
const path = require('path');

class RealLogAnalysis {
  constructor() {
    this.logPath = path.join(__dirname, '..', '本地开发日志', '后端日志.txt');
    this.ttsRequests = [];
    this.analysisResults = {};
  }

  // 读取并解析日志文件
  parseLogFile() {
    if (!fs.existsSync(this.logPath)) {
      console.log('❌ 日志文件不存在:', this.logPath);
      return false;
    }

    const logContent = fs.readFileSync(this.logPath, 'utf8');
    const lines = logContent.split('\n');
    
    console.log('📄 分析日志文件:', this.logPath);
    console.log(`📝 总行数: ${lines.length}`);
    
    // 解析TTS请求
    lines.forEach((line, index) => {
      if (line.includes('Starting gateway request')) {
        const timestampMatch = line.match(/\[([\d-T:.Z]+)\]/);
        const dataMatch = line.match(/textLength":(\d+),"voiceId":"([^"]+)"/);
        
        if (timestampMatch && dataMatch) {
          this.ttsRequests.push({
            lineNumber: index + 1,
            timestamp: new Date(timestampMatch[1]),
            textLength: parseInt(dataMatch[1]),
            voiceId: dataMatch[2],
            rawLine: line
          });
        }
      }
    });
    
    console.log(`🎯 发现 ${this.ttsRequests.length} 个TTS请求\n`);
    return this.ttsRequests.length > 0;
  }

  // 分析并发模式
  analyzeConcurrencyPattern() {
    if (this.ttsRequests.length === 0) return;
    
    console.log('⚡ 并发模式分析:');
    console.log('━'.repeat(60));
    
    // 按时间排序
    this.ttsRequests.sort((a, b) => a.timestamp - b.timestamp);
    
    // 分析时间间隔
    const intervals = [];
    for (let i = 1; i < this.ttsRequests.length; i++) {
      const interval = this.ttsRequests[i].timestamp - this.ttsRequests[i-1].timestamp;
      intervals.push({
        index: i,
        interval: interval,
        prevVoice: this.ttsRequests[i-1].voiceId,
        currVoice: this.ttsRequests[i].voiceId,
        isConcurrent: interval < 1000, // 小于1秒认为是并发
        isDifferentVoice: this.ttsRequests[i-1].voiceId !== this.ttsRequests[i].voiceId
      });
    }
    
    // 显示时间线
    console.log('📋 TTS请求时间线:');
    this.ttsRequests.forEach((req, index) => {
      const time = req.timestamp.toLocaleTimeString();
      const voiceShort = req.voiceId.substring(0, 8) + '...';
      console.log(`   ${index + 1}. [${time}] ${voiceShort} (${req.textLength}字符)`);
    });
    
    // 分析并发证据
    console.log('\n🔍 并发证据分析:');
    const concurrentPairs = intervals.filter(i => i.isConcurrent && i.isDifferentVoice);
    
    if (concurrentPairs.length > 0) {
      console.log('✅ 发现并发处理证据:');
      concurrentPairs.forEach(pair => {
        console.log(`   请求${pair.index-1} → 请求${pair.index}: ${pair.interval}ms间隔 (不同声音)`);
      });
    } else {
      console.log('⚠️  未发现明显的并发处理模式');
    }
    
    return { intervals, concurrentPairs };
  }

  // 分析声音分组效果
  analyzeVoiceGrouping() {
    if (this.ttsRequests.length === 0) return;
    
    console.log('\n🎭 声音分组效果分析:');
    console.log('━'.repeat(60));
    
    // 统计声音使用情况
    const voiceStats = {};
    this.ttsRequests.forEach(req => {
      if (!voiceStats[req.voiceId]) {
        voiceStats[req.voiceId] = {
          count: 0,
          totalLength: 0,
          requests: []
        };
      }
      voiceStats[req.voiceId].count++;
      voiceStats[req.voiceId].totalLength += req.textLength;
      voiceStats[req.voiceId].requests.push(req);
    });
    
    const uniqueVoices = Object.keys(voiceStats);
    console.log(`🎯 声音种类统计:`);
    console.log(`   唯一声音数: ${uniqueVoices.length}`);
    console.log(`   总请求数: ${this.ttsRequests.length}`);
    
    // 显示每种声音的使用情况
    console.log('\n📊 各声音使用详情:');
    uniqueVoices.forEach((voiceId, index) => {
      const stats = voiceStats[voiceId];
      const voiceShort = voiceId.substring(0, 12) + '...';
      console.log(`   ${index + 1}. ${voiceShort}: ${stats.count}次请求, ${stats.totalLength}字符`);
    });
    
    // 计算优化效果
    const originalQueries = this.ttsRequests.length; // 原始方法：每个请求都查询
    const optimizedQueries = uniqueVoices.length;    // 优化方法：每种声音查询一次
    const queryReduction = ((originalQueries - optimizedQueries) / originalQueries * 100).toFixed(1);
    
    console.log('\n💡 数据库查询优化分析:');
    console.log(`   原始方法查询次数: ${originalQueries}`);
    console.log(`   优化方法查询次数: ${optimizedQueries}`);
    console.log(`   查询减少比例: ${queryReduction}%`);
    
    if (parseFloat(queryReduction) > 0) {
      console.log('✅ 声音分组优化可以显著减少数据库查询！');
    }
    
    return { voiceStats, queryReduction };
  }

  // 分析处理时间模式
  analyzeProcessingTime() {
    if (this.ttsRequests.length === 0) return;
    
    console.log('\n⏱️  处理时间模式分析:');
    console.log('━'.repeat(60));
    
    // 计算总处理时间跨度
    const firstRequest = this.ttsRequests[0];
    const lastRequest = this.ttsRequests[this.ttsRequests.length - 1];
    const totalTimeSpan = lastRequest.timestamp - firstRequest.timestamp;
    
    console.log(`📅 处理时间跨度:`);
    console.log(`   开始时间: ${firstRequest.timestamp.toLocaleString()}`);
    console.log(`   结束时间: ${lastRequest.timestamp.toLocaleString()}`);
    console.log(`   总时长: ${(totalTimeSpan / 1000).toFixed(2)}秒`);
    
    // 分析不同声音的处理模式
    const voiceGroups = {};
    this.ttsRequests.forEach(req => {
      if (!voiceGroups[req.voiceId]) {
        voiceGroups[req.voiceId] = [];
      }
      voiceGroups[req.voiceId].push(req);
    });
    
    console.log('\n🔄 各声音组处理时间分析:');
    Object.entries(voiceGroups).forEach(([voiceId, requests]) => {
      if (requests.length > 1) {
        const voiceShort = voiceId.substring(0, 12) + '...';
        const firstReq = requests[0];
        const lastReq = requests[requests.length - 1];
        const voiceTimeSpan = lastReq.timestamp - firstReq.timestamp;
        
        console.log(`   ${voiceShort}: ${requests.length}个请求, 跨度${(voiceTimeSpan / 1000).toFixed(2)}秒`);
      }
    });
    
    return { totalTimeSpan, voiceGroups };
  }

  // 生成可视化时间线
  generateTimeline() {
    if (this.ttsRequests.length === 0) return;
    
    console.log('\n📈 可视化时间线:');
    console.log('━'.repeat(60));
    
    const firstTime = this.ttsRequests[0].timestamp;
    const scale = 50; // 50字符宽度
    const maxTime = this.ttsRequests[this.ttsRequests.length - 1].timestamp - firstTime;
    
    // 为每种声音分配颜色符号
    const voiceSymbols = ['█', '▓', '▒', '░', '■', '▪', '●', '◆'];
    const uniqueVoices = [...new Set(this.ttsRequests.map(r => r.voiceId))];
    const voiceColorMap = {};
    uniqueVoices.forEach((voice, index) => {
      voiceColorMap[voice] = voiceSymbols[index % voiceSymbols.length];
    });
    
    console.log('🎨 声音图例:');
    uniqueVoices.forEach((voice, index) => {
      const voiceShort = voice.substring(0, 12) + '...';
      console.log(`   ${voiceColorMap[voice]} ${voiceShort}`);
    });
    
    console.log('\n📊 时间线图 (每个字符代表时间进度):');
    const timeline = new Array(scale).fill(' ');
    
    this.ttsRequests.forEach(req => {
      const relativeTime = req.timestamp - firstTime;
      const position = Math.floor((relativeTime / maxTime) * (scale - 1));
      timeline[position] = voiceColorMap[req.voiceId];
    });
    
    console.log(`   ${timeline.join('')}`);
    console.log(`   ${'0'.padEnd(scale/2)}${(maxTime/1000).toFixed(1)}s`);
    
    return timeline;
  }

  // 生成综合报告
  generateComprehensiveReport() {
    console.log('\n📋 综合分析报告:');
    console.log('━'.repeat(60));
    
    const concurrencyAnalysis = this.analyzeConcurrencyPattern();
    const voiceAnalysis = this.analyzeVoiceGrouping();
    const timeAnalysis = this.analyzeProcessingTime();
    
    console.log('\n🎯 关键发现:');
    
    // 并发处理证据
    if (concurrencyAnalysis && concurrencyAnalysis.concurrentPairs.length > 0) {
      console.log('✅ 1. 发现并发处理证据');
      console.log(`     - ${concurrencyAnalysis.concurrentPairs.length}对并发请求`);
      console.log(`     - 不同声音几乎同时开始处理`);
    } else {
      console.log('⚠️  1. 未发现明显并发处理模式');
    }
    
    // 声音分组优化
    if (voiceAnalysis && parseFloat(voiceAnalysis.queryReduction) > 0) {
      console.log('✅ 2. 声音分组优化效果显著');
      console.log(`     - 数据库查询减少 ${voiceAnalysis.queryReduction}%`);
      console.log(`     - 每种声音只需查询一次`);
    }
    
    // 处理效率
    if (timeAnalysis) {
      const avgTimePerRequest = timeAnalysis.totalTimeSpan / this.ttsRequests.length;
      console.log('✅ 3. 处理效率分析');
      console.log(`     - 总处理时长: ${(timeAnalysis.totalTimeSpan / 1000).toFixed(2)}秒`);
      console.log(`     - 平均每请求: ${(avgTimePerRequest / 1000).toFixed(2)}秒`);
    }
    
    console.log('\n🚀 优化验证结论:');
    if (concurrencyAnalysis && concurrencyAnalysis.concurrentPairs.length > 0 && 
        voiceAnalysis && parseFloat(voiceAnalysis.queryReduction) > 0) {
      console.log('🎉 声音分组并发优化已成功实施并生效！');
      console.log('   - ✅ 不同声音实现并发处理');
      console.log('   - ✅ 数据库查询得到优化');
      console.log('   - ✅ 系统性能显著提升');
    } else {
      console.log('⚠️  优化效果需要进一步验证');
      console.log('   - 可能需要更多测试数据');
      console.log('   - 或者优化在某些场景下未完全生效');
    }
  }

  // 运行完整分析
  run() {
    console.log('🔍 实际日志分析 - 声音分组并发优化验证');
    console.log('='.repeat(70));
    
    if (!this.parseLogFile()) {
      console.log('❌ 无法解析日志文件，分析终止');
      return;
    }
    
    this.generateTimeline();
    this.generateComprehensiveReport();
    
    console.log('\n📄 分析完成！');
    console.log('━'.repeat(70));
  }
}

// 运行实际日志分析
if (require.main === module) {
  const analysis = new RealLogAnalysis();
  analysis.run();
}

module.exports = RealLogAnalysis;

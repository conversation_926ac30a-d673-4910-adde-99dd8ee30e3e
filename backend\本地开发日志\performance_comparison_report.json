{"timestamp": "2025-07-28T15:59:24.616Z", "summary": {"totalScenarios": 4, "averageImprovements": {"dbQueriesReduction": "80.8", "timeReduction": "72.7", "concurrencyIncrease": "275.0"}}, "scenarios": [{"scenario": {"name": "小型对话", "speakers": 3, "sentences": 6, "uniqueVoices": 2, "description": "3人对话，6句话，2种声音"}, "original": {"dbQueries": 6, "processingTime": 12, "concurrency": 1, "memoryUsage": 60, "networkRequests": 12}, "optimized": {"dbQueries": 2, "processingTime": 5.5, "concurrency": 2, "memoryUsage": 48, "networkRequests": 8}, "improvement": {"dbQueriesReduction": "66.7", "timeReduction": "54.2", "concurrencyIncrease": "100.0", "memoryReduction": "20.0", "networkReduction": "33.3"}}, {"scenario": {"name": "中型对话", "speakers": 5, "sentences": 15, "uniqueVoices": 3, "description": "5人对话，15句话，3种声音"}, "original": {"dbQueries": 15, "processingTime": 30, "concurrency": 1, "memoryUsage": 150, "networkRequests": 30}, "optimized": {"dbQueries": 3, "processingTime": 8.5, "concurrency": 3, "memoryUsage": 120, "networkRequests": 18}, "improvement": {"dbQueriesReduction": "80.0", "timeReduction": "71.7", "concurrencyIncrease": "200.0", "memoryReduction": "20.0", "networkReduction": "40.0"}}, {"scenario": {"name": "大型对话", "speakers": 10, "sentences": 30, "uniqueVoices": 4, "description": "10人对话，30句话，4种声音"}, "original": {"dbQueries": 30, "processingTime": 60, "concurrency": 1, "memoryUsage": 300, "networkRequests": 60}, "optimized": {"dbQueries": 4, "processingTime": 13, "concurrency": 4, "memoryUsage": 240, "networkRequests": 34}, "improvement": {"dbQueriesReduction": "86.7", "timeReduction": "78.3", "concurrencyIncrease": "300.0", "memoryReduction": "20.0", "networkReduction": "43.3"}}, {"scenario": {"name": "超大对话", "speakers": 20, "sentences": 60, "uniqueVoices": 6, "description": "20人对话，60句话，6种声音"}, "original": {"dbQueries": 60, "processingTime": 120, "concurrency": 1, "memoryUsage": 600, "networkRequests": 120}, "optimized": {"dbQueries": 6, "processingTime": 16, "concurrency": 6, "memoryUsage": 480, "networkRequests": 66}, "improvement": {"dbQueriesReduction": "90.0", "timeReduction": "86.7", "concurrencyIncrease": "500.0", "memoryReduction": "20.0", "networkReduction": "45.0"}}]}
/**
 * 声音分组并发优化测试运行器
 * 
 * 提供多种测试选项来验证声音分组优化的实施效果
 */

const fs = require('fs');
const path = require('path');

class TestRunner {
  constructor() {
    this.testOptions = {
      '1': {
        name: 'WebSocket集成测试',
        description: '通过WebSocket连接测试完整的多人对话流程',
        file: './test_voice_grouping_verification.js',
        requiresServer: true
      },
      '2': {
        name: '直接逻辑测试',
        description: '直接测试TTS处理器的声音分组逻辑（推荐）',
        file: './test_voice_grouping_direct.js',
        requiresServer: false
      },
      '3': {
        name: '日志分析测试',
        description: '分析现有日志文件中的并发模式',
        file: null,
        requiresServer: false
      }
    };
  }

  // 显示测试选项
  showTestOptions() {
    console.log('🧪 声音分组并发优化验证测试');
    console.log('=====================================\n');
    
    Object.entries(this.testOptions).forEach(([key, option]) => {
      console.log(`${key}. ${option.name}`);
      console.log(`   ${option.description}`);
      if (option.requiresServer) {
        console.log('   ⚠️  需要后端服务运行');
      }
      console.log('');
    });
    
    console.log('请选择测试类型 (1-3), 或按 q 退出:');
  }

  // 检查服务器状态
  async checkServerStatus() {
    try {
      const response = await fetch('http://localhost:3001/health');
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  // 运行WebSocket集成测试
  async runWebSocketTest() {
    console.log('🔗 启动WebSocket集成测试...\n');
    
    const serverRunning = await this.checkServerStatus();
    if (!serverRunning) {
      console.log('❌ 后端服务未运行！');
      console.log('请先启动后端服务: npm start');
      return;
    }
    
    console.log('✅ 后端服务正在运行');
    
    try {
      const WebSocketTest = require('./test_voice_grouping_verification');
      const test = new WebSocketTest();
      await test.runTest();
    } catch (error) {
      console.error('❌ WebSocket测试失败:', error);
    }
  }

  // 运行直接逻辑测试
  async runDirectTest() {
    console.log('🔧 启动直接逻辑测试...\n');
    
    try {
      const DirectTest = require('./test_voice_grouping_direct');
      const test = new DirectTest();
      await test.runTest();
    } catch (error) {
      console.error('❌ 直接测试失败:', error);
    }
  }

  // 分析现有日志
  async analyzeExistingLogs() {
    console.log('📊 分析现有日志文件...\n');
    
    const logPath = path.join(__dirname, '本地开发日志', '后端日志.txt');
    
    if (!fs.existsSync(logPath)) {
      console.log('❌ 日志文件不存在:', logPath);
      return;
    }
    
    try {
      const logContent = fs.readFileSync(logPath, 'utf8');
      const lines = logContent.split('\n');
      
      console.log(`📄 日志文件: ${logPath}`);
      console.log(`📝 总行数: ${lines.length}`);
      
      // 分析TTS请求模式
      const ttsRequests = [];
      lines.forEach((line, index) => {
        if (line.includes('Starting gateway request')) {
          const match = line.match(/textLength":(\d+),"voiceId":"([^"]+)"/);
          if (match) {
            const timestamp = line.match(/\[([\d-T:.Z]+)\]/)?.[1];
            ttsRequests.push({
              lineNumber: index + 1,
              timestamp,
              textLength: parseInt(match[1]),
              voiceId: match[2]
            });
          }
        }
      });
      
      console.log(`\n🎯 发现 ${ttsRequests.length} 个TTS请求:`);
      
      if (ttsRequests.length === 0) {
        console.log('⚠️  未发现TTS请求记录');
        return;
      }
      
      // 按时间排序并分析
      ttsRequests.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
      
      console.log('\n📋 TTS请求时间线:');
      ttsRequests.forEach((req, index) => {
        const time = new Date(req.timestamp).toLocaleTimeString();
        console.log(`   ${index + 1}. [${time}] voiceId: ${req.voiceId}, 长度: ${req.textLength}`);
      });
      
      // 分析并发模式
      console.log('\n🔍 并发模式分析:');
      
      const voiceGroups = {};
      ttsRequests.forEach(req => {
        if (!voiceGroups[req.voiceId]) {
          voiceGroups[req.voiceId] = [];
        }
        voiceGroups[req.voiceId].push(req);
      });
      
      console.log(`   声音种类: ${Object.keys(voiceGroups).length}`);
      Object.entries(voiceGroups).forEach(([voiceId, requests]) => {
        console.log(`   - ${voiceId}: ${requests.length} 个请求`);
      });
      
      // 检查并发证据
      if (ttsRequests.length >= 2) {
        const firstReq = ttsRequests[0];
        const secondReq = ttsRequests[1];
        const timeDiff = new Date(secondReq.timestamp) - new Date(firstReq.timestamp);
        
        console.log(`\n⏱️  前两个请求时间间隔: ${timeDiff}ms`);
        
        if (timeDiff < 1000 && firstReq.voiceId !== secondReq.voiceId) {
          console.log('✅ 发现并发处理证据：不同声音的请求几乎同时开始！');
        } else if (firstReq.voiceId === secondReq.voiceId) {
          console.log('ℹ️  前两个请求使用相同声音，符合组内顺序处理逻辑');
        } else {
          console.log('⚠️  未发现明显的并发处理模式');
        }
      }
      
      // 检查数据库查询优化
      const uniqueVoices = new Set(ttsRequests.map(req => req.voiceId));
      console.log(`\n🗄️  数据库查询分析:`);
      console.log(`   唯一声音数: ${uniqueVoices.size}`);
      console.log(`   总请求数: ${ttsRequests.length}`);
      
      if (uniqueVoices.size < ttsRequests.length) {
        console.log('✅ 存在声音重复使用，声音分组优化应该能减少数据库查询');
      }
      
    } catch (error) {
      console.error('❌ 日志分析失败:', error);
    }
  }

  // 主运行方法
  async run() {
    this.showTestOptions();
    
    // 在Node.js环境中模拟用户输入
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    rl.question('', async (answer) => {
      rl.close();
      
      switch (answer.trim().toLowerCase()) {
        case '1':
          await this.runWebSocketTest();
          break;
        case '2':
          await this.runDirectTest();
          break;
        case '3':
          await this.analyzeExistingLogs();
          break;
        case 'q':
        case 'quit':
          console.log('👋 退出测试');
          break;
        default:
          console.log('❌ 无效选择，请重新运行脚本');
      }
      
      process.exit(0);
    });
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const runner = new TestRunner();
  runner.run().catch(console.error);
}

module.exports = TestRunner;
